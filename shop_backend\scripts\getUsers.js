import mongoose from 'mongoose';
import dotenv from 'dotenv';
import { User } from '../models/User.model.js';
import connectDB from '../db/connection.db.js';

dotenv.config({ path: './shop_backend/.env' });

const getUsers = async () => {
    try {
        await connectDB();
        console.log("Connected to MongoDB.");

        const users = await User.find().select("-password -refreshToken");
        console.log("All Users:");
        if (users.length === 0) {
            console.log("No users found.");
        } else {
            users.forEach(user => {
                console.log(user.toJSON());
            });
        }
    } catch (error) {
        console.error("Error fetching users:", error);
    } finally {
        mongoose.disconnect();
        console.log("Disconnected from MongoDB.");
    }
};

getUsers();
