// import mongoose from 'mongoose';

// const customerSchema = new mongoose.Schema({
//   email: { type: String, required: true, unique: true },
//   fullname: { type: String, required: true },
//   adhar: { type: String, required: true },
//   pan: { type: String, required: true },
//   contact: { type: String, required: true },
//   password: { type: String, required: true },

//   orders: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Order' }],
// }, { timestamps: true });

// export default mongoose.model('Customer', customerSchema);


// by sahin

import mongoose, { Schema } from "mongoose";
import jwt from "jsonwebtoken";
import bcrypt from "bcrypt";

const userSchema = new Schema(
    {
        fullName: {
            type: String,
            required: true,
            trim: true,
            index: true
        },
        
        email: {
            type: String,
            required: true,
            unique: true,
            lowercase: true,
            trim: true
        },
        
        aadhar: {
            type: String,
            required: true,
            unique: true,
            validate: {
                validator: function(v) {
                    return /^\d{12}$/.test(v);
                },
                message: props => `${props.value} is not a valid 12-digit Aadhar number!`
            }
        },
        pan: {
            type: String,
            sparse: true // Allows multiple documents to have a null value for pan
        },
        phone: {
            type: String,
            required: true
        },
        userType: {
            type: String,
            enum: ['user', 'renter', 'admin', 'super admin', 'customer'],
            required: true
        },
        orders: [{
            type: String,
        }],
        password: {
            type: String,
            required: [true, 'Password is required']
        },
        refreshToken: {
            type: String
        }
    },
    {
        timestamps: true
    }
);

// Hash password before saving
userSchema.pre("save", async function (next) {
    if (!this.isModified("password")) return next();
    this.password = await bcrypt.hash(this.password, 10);
    next();
});

// Method to validate password
userSchema.methods.isPasswordCorrect = async function (password) {
    return await bcrypt.compare(password, this.password);
};

// Method to generate access token
userSchema.methods.generateAccessToken = function () {
    return jwt.sign(
        {
            _id: this._id,
            email: this.email,
            username: this.username,
            fullName: this.fullName
        },
        process.env.ACCESS_TOKEN_SECRET,
        {
            expiresIn: process.env.ACCESS_TOKEN_EXPIRY
        }
    );
};

// Method to generate refresh token
userSchema.methods.generateRefreshToken = function () {
    return jwt.sign(
        {
            _id: this._id
        },
        process.env.REFRESH_TOKEN_SECRET,
        {
            expiresIn: process.env.REFRESH_TOKEN_EXPIRY
        }
    );
};

export const User = mongoose.model("User", userSchema);
