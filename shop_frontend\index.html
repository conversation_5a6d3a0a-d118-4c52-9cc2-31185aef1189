<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Food Vendor Supplier Dashboard</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://unpkg.com/framer-motion@10/dist/framer-motion.js"></script>
    <script src="https://unpkg.com/lucide-react@0.263.1/dist/umd/lucide-react.js"></script>
    <script src="https://unpkg.com/react-icons@4.10.1/fa/index.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
                'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
                sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
    </style>
</head>
<body>
    <div id="root"></div>
    
    <script type="text/babel">
        // Mock localStorage for testing
        if (!localStorage.getItem('accessToken')) {
            localStorage.setItem('accessToken', 'mock-token-for-testing');
            localStorage.setItem('supplierId', '507f1f77bcf86cd799439011');
            localStorage.setItem('userType', 'supplier');
        }

        // Simple test component to verify the backend connection
        const TestDashboard = () => {
            const [status, setStatus] = React.useState('Testing connection...');
            const [suppliers, setSuppliers] = React.useState([]);

            React.useEffect(() => {
                // Test backend connection
                fetch('http://localhost:5000/supplier')
                    .then(response => response.json())
                    .then(data => {
                        setStatus('✅ Backend connected successfully!');
                        setSuppliers(data.data || []);
                    })
                    .catch(error => {
                        setStatus('❌ Backend connection failed: ' + error.message);
                    });
            }, []);

            const testLogin = async () => {
                try {
                    const response = await fetch('http://localhost:5000/supplier/login', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            email: '<EMAIL>',
                            password: 'password123'
                        })
                    });
                    
                    const data = await response.json();
                    if (response.ok) {
                        setStatus('✅ Login test successful!');
                        localStorage.setItem('accessToken', data.data.accessToken);
                        localStorage.setItem('supplierId', data.data.supplier._id);
                    } else {
                        setStatus('⚠️ Login test failed: ' + data.message);
                    }
                } catch (error) {
                    setStatus('❌ Login test error: ' + error.message);
                }
            };

            return (
                <div className="min-h-screen bg-gray-50 p-8">
                    <div className="max-w-4xl mx-auto">
                        <h1 className="text-3xl font-bold text-gray-900 mb-8">Food Vendor Backend Test</h1>
                        
                        <div className="bg-white rounded-lg shadow p-6 mb-6">
                            <h2 className="text-xl font-semibold mb-4">Connection Status</h2>
                            <p className="text-lg">{status}</p>
                        </div>

                        <div className="bg-white rounded-lg shadow p-6 mb-6">
                            <h2 className="text-xl font-semibold mb-4">Test Actions</h2>
                            <button 
                                onClick={testLogin}
                                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors mr-4"
                            >
                                Test Login
                            </button>
                        </div>

                        <div className="bg-white rounded-lg shadow p-6">
                            <h2 className="text-xl font-semibold mb-4">Suppliers ({suppliers.length})</h2>
                            {suppliers.length === 0 ? (
                                <p className="text-gray-500">No suppliers found</p>
                            ) : (
                                <div className="space-y-2">
                                    {suppliers.map((supplier, index) => (
                                        <div key={index} className="p-3 bg-gray-50 rounded border">
                                            <p><strong>Name:</strong> {supplier.fullname}</p>
                                            <p><strong>Email:</strong> {supplier.email}</p>
                                            <p><strong>Verified:</strong> {supplier.isVerified ? 'Yes' : 'No'}</p>
                                        </div>
                                    ))}
                                </div>
                            )}
                        </div>

                        <div className="mt-8 text-center">
                            <p className="text-gray-600">
                                Backend is running on: <strong>http://localhost:5000</strong>
                            </p>
                            <p className="text-gray-600 mt-2">
                                Available endpoints: /supplier, /product, /order, /user
                            </p>
                        </div>
                    </div>
                </div>
            );
        };

        ReactDOM.render(<TestDashboard />, document.getElementById('root'));
    </script>
</body>
</html>
