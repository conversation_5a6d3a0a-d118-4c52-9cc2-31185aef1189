import { Product } from '../models/Product.model.js';
import { asyncHandler } from '../utils/asyncHandler.js';
import { ApiError } from '../utils/ApiError.js';
import { ApiResponse } from '../utils/ApiResponse.js';

// Create Product
export const createProduct = asyncHandler(async (req, res) => {
  const { supplierId, type, review, pricePerUnit, dateOfProduce } = req.body;

  if (!supplierId || !type || !pricePerUnit || !dateOfProduce) {
    throw new ApiError(400, 'Required fields missing');
  }

  const product = await Product.create({
    supplierId, type, review, pricePerUnit, dateOfProduce
  });

  res.status(201).json(new ApiResponse(201, product, 'Product created successfully'));
});

// Get All Products
export const getAllProducts = asyncHandler(async (req, res) => {
  const products = await Product.find().populate('supplierId');
  res.status(200).json(new ApiResponse(200, products, 'Products fetched successfully'));
});

// Get Product by ID
export const getProductById = asyncHandler(async (req, res) => {
  const product = await Product.findById(req.params.id).populate('supplierId');

  if (!product) throw new ApiError(404, 'Product not found');

  res.status(200).json(new ApiResponse(200, product, 'Product fetched successfully'));
});

// Update Product
export const updateProduct = asyncHandler(async (req, res) => {
  const product = await Product.findByIdAndUpdate(req.params.id, req.body, { new: true });

  if (!product) throw new ApiError(404, 'Product not found');

  res.status(200).json(new ApiResponse(200, product, 'Product updated successfully'));
});

// Delete Product
export const deleteProduct = asyncHandler(async (req, res) => {
  const product = await Product.findByIdAndDelete(req.params.id);

  if (!product) throw new ApiError(404, 'Product not found');

  res.status(200).json(new ApiResponse(200, null, 'Product deleted successfully'));
});

// Create Product by Supplier (from authenticated supplier)
export const createProductBySupplier = asyncHandler(async (req, res) => {
  const { type, review, pricePerUnit, dateOfProduce } = req.body;

  // Get supplier ID from authenticated user
  const supplierId = req.user._id;

  if (!type || !pricePerUnit || !dateOfProduce) {
    throw new ApiError(400, 'Required fields missing');
  }

  const product = await Product.create({
    supplierId, type, review, pricePerUnit, dateOfProduce
  });

  const populatedProduct = await Product.findById(product._id).populate('supplierId');

  res.status(201).json(new ApiResponse(201, populatedProduct, 'Product created successfully'));
});

// Get Products by Supplier ID
export const getProductsBySupplier = asyncHandler(async (req, res) => {
  const { supplierId } = req.params;

  if (!supplierId) {
    throw new ApiError(400, 'Supplier ID is required');
  }

  const products = await Product.find({ supplierId }).populate('supplierId');
  res.status(200).json(new ApiResponse(200, products, 'Supplier products fetched successfully'));
});
