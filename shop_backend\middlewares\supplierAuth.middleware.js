import { Supplier } from "../models/Supplier.model.js";
import { ApiError } from "../utils/ApiError.js";
import { asyncHandler } from "../utils/asyncHandler.js";

export const verifySupplier = asyncHandler(async (req, res, next) => {
    // This middleware assumes verifyJWT has already run and populated req.user
    if (!req.user) {
        throw new ApiError(401, "Unauthorized: User not authenticated.");
    }

    // Check if the authenticated user's email exists in the Supplier model
    const supplier = await Supplier.findOne({ email: req.user.email });

    if (!supplier) {
        throw new ApiError(403, "Forbidden: Only registered suppliers can perform this action.");
    }

    // Attach the supplier document to the request for further use
    req.supplier = supplier;
    next();
});
