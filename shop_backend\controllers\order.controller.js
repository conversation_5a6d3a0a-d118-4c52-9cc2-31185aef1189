import { Order } from '../models/Order.model.js';
import { asyncHandler } from '../utils/asyncHandler.js';
import { ApiError } from '../utils/ApiError.js';
import { ApiResponse } from '../utils/ApiResponse.js';

// Create Order
export const createOrder = asyncHandler(async (req, res) => {
  const { productId, customerId, supplierId, totalPrice, quantity } = req.body;

  if (!productId || !customerId || !supplierId || !totalPrice || !quantity) {
    throw new ApiError(400, 'Required fields missing');
  }

  const order = await Order.create({
    productId, customerId, supplierId, totalPrice, quantity
  });

  res.status(201).json(new ApiResponse(201, order, 'Order created successfully'));
});

// Get All Orders
export const getAllOrders = asyncHandler(async (req, res) => {
  const orders = await Order.find()
    .populate('productId')
    .populate('customerId')
    .populate('supplierId');

  res.status(200).json(new ApiResponse(200, orders, 'Orders fetched successfully'));
});

// Get Order by ID
export const getOrderById = asyncHandler(async (req, res) => {
  const order = await Order.findById(req.params.id)
    .populate('productId')
    .populate('customerId')
    .populate('supplierId');

  if (!order) throw new ApiError(404, 'Order not found');

  res.status(200).json(new ApiResponse(200, order, 'Order fetched successfully'));
});

// Update Order
export const updateOrder = asyncHandler(async (req, res) => {
  const order = await Order.findByIdAndUpdate(req.params.id, req.body, { new: true });

  if (!order) throw new ApiError(404, 'Order not found');

  res.status(200).json(new ApiResponse(200, order, 'Order updated successfully'));
});

// Delete Order
export const deleteOrder = asyncHandler(async (req, res) => {
  const order = await Order.findByIdAndDelete(req.params.id);

  if (!order) throw new ApiError(404, 'Order not found');

  res.status(200).json(new ApiResponse(200, null, 'Order deleted successfully'));
});
