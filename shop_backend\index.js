import express from 'express';
import mongoose from 'mongoose';
import dotenv from 'dotenv';
import cors from 'cors';
import { app } from "./app.js";
import connectDB from "./db/connection.db.js";


dotenv.config({ path: '.env' });

app.use(cors());
app.use(express.json());

// Sample route
app.get("/", (req, res) => {
    res.json({ msg: "server is running" });
});

// Connect DB and start server
connectDB()
.then(() => {
    app.listen(process.env.PORT || 8080, () => {
        console.log(`⚙️ Server is running at port : ${process.env.PORT}`);
    });
})
.catch((err) => {
    console.log("MONGO db connection failed !!! ", err);
});
