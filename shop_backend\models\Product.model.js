// models/Product.js
import mongoose from 'mongoose';

const productSchema = new mongoose.Schema({
  supplierId: { type: mongoose.Schema.Types.ObjectId, ref: 'Supplier', required: true },

  type: { type: String, required: true },
  review: { type: String },
  pricePerUnit: { type: Number, required: true },
  dateOfProduce: { type: Date, required: true },
}, { timestamps: true });

export const Product = mongoose.model('Product', productSchema);
