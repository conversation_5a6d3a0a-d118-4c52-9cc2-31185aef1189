import { Supplier } from '../models/Supplier.model.js';
import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import { asyncHandler } from '../utils/asyncHandler.js';
import { ApiError } from '../utils/ApiError.js';
import { ApiResponse } from '../utils/ApiResponse.js';

// Function to generate tokens for suppliers
const generateAccessAndRefreshTokens = async (supplierId) => {
  try {
    const supplier = await Supplier.findById(supplierId);

    const accessToken = jwt.sign(
      {
        _id: supplier._id,
        email: supplier.email,
        fullname: supplier.fullname
      },
      process.env.ACCESS_TOKEN_SECRET,
      {
        expiresIn: process.env.ACCESS_TOKEN_EXPIRY || '1d'
      }
    );

    const refreshToken = jwt.sign(
      {
        _id: supplier._id
      },
      process.env.REFRESH_TOKEN_SECRET,
      {
        expiresIn: process.env.REFRESH_TOKEN_EXPIRY || '10d'
      }
    );

    return { accessToken, refreshToken };
  } catch (error) {
    throw new ApiError(500, "Something went wrong while generating tokens");
  }
};

// Register Supplier
export const registerSupplier = asyncHandler(async (req, res) => {
  const { email, fullname, adhar, pan, contact, password, certificateCode } = req.body;

  if (!email || !fullname || !adhar || !pan || !contact || !password) {
    throw new ApiError(400, 'Required fields missing');
  }

  const existing = await Supplier.findOne({ email });
  if (existing) throw new ApiError(409, 'Supplier already exists');

  const hashedPassword = await bcrypt.hash(password, 10);

  const supplier = await Supplier.create({
    email, fullname, adhar, pan, contact, password: hashedPassword, certificateCode
  });

  // Generate tokens
  const { accessToken, refreshToken } = await generateAccessAndRefreshTokens(supplier._id);

  // Get supplier details without sensitive info
  const createdSupplier = await Supplier.findById(supplier._id).select("-password");

  // Set cookie options
  const options = {
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    sameSite: 'strict'
  };

  res.status(201)
    .cookie("accessToken", accessToken, options)
    .cookie("refreshToken", refreshToken, options)
    .json(new ApiResponse(201, {
      supplier: createdSupplier,
      accessToken,
      refreshToken
    }, 'Supplier registered successfully'));
});

// Login Supplier
export const loginSupplier = asyncHandler(async (req, res) => {
  const { email, password } = req.body;

  if (!email || !password) {
    throw new ApiError(400, "Email and password are required");
  }

  const supplier = await Supplier.findOne({ email });
  if (!supplier) {
    throw new ApiError(404, "Supplier not found");
  }

  const isPasswordValid = await bcrypt.compare(password, supplier.password);
  if (!isPasswordValid) {
    throw new ApiError(401, "Invalid credentials");
  }

  // Generate tokens
  const { accessToken, refreshToken } = await generateAccessAndRefreshTokens(supplier._id);

  const loggedInSupplier = await Supplier.findById(supplier._id).select("-password");

  const cookieOptions = {
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    sameSite: "strict"
  };

  return res
    .status(200)
    .cookie("accessToken", accessToken, {
      ...cookieOptions,
      maxAge: 24 * 60 * 60 * 1000 // 1 day in milliseconds
    })
    .cookie("refreshToken", refreshToken, {
      ...cookieOptions,
      maxAge: 10 * 24 * 60 * 60 * 1000 // 10 days in milliseconds
    })
    .json(
      new ApiResponse(200, {
        supplier: loggedInSupplier,
        accessToken,
        refreshToken,
      }, "Login successful")
    );
});

// Get All Suppliers
export const getAllSuppliers = asyncHandler(async (req, res) => {
  const suppliers = await Supplier.find().populate('orders');
  res.status(200).json(new ApiResponse(200, suppliers, 'Suppliers fetched successfully'));
});

// Get Supplier by ID
export const getSupplierById = asyncHandler(async (req, res) => {
  const supplier = await Supplier.findById(req.params.id).populate('orders');

  if (!supplier) throw new ApiError(404, 'Supplier not found');

  res.status(200).json(new ApiResponse(200, supplier, 'Supplier fetched successfully'));
});

// Update Supplier
export const updateSupplier = asyncHandler(async (req, res) => {
  const supplier = await Supplier.findByIdAndUpdate(req.params.id, req.body, { new: true });

  if (!supplier) throw new ApiError(404, 'Supplier not found');

  res.status(200).json(new ApiResponse(200, supplier, 'Supplier updated successfully'));
});

// Delete Supplier
export const deleteSupplier = asyncHandler(async (req, res) => {
  const supplier = await Supplier.findByIdAndDelete(req.params.id);

  if (!supplier) throw new ApiError(404, 'Supplier not found');

  res.status(200).json(new ApiResponse(200, null, 'Supplier deleted successfully'));
});
// Verify Supplier
export const verifySupplier = asyncHandler(async (req, res) => {
  const { supplierId, certificateCode } = req.body;

  if (!supplierId || !certificateCode) {
    throw new ApiError(400, 'Required fields missing');
  }

  const supplier = await Supplier.findById(supplierId);
  if (!supplier) throw new ApiError(404, 'Supplier not found');

  if (supplier.certificateCode !== certificateCode) {
    throw new ApiError(400, 'Invalid certificate code');
  }

  supplier.isVerified = true;
  await supplier.save();

  res.status(200).json(new ApiResponse(200, supplier, 'Supplier verified successfully'));
});

// Logout Supplier
export const logoutSupplier = asyncHandler(async (req, res) => {
  const cookieOptions = {
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    sameSite: "strict"
  };

  return res
    .status(200)
    .clearCookie("accessToken", cookieOptions)
    .clearCookie("refreshToken", cookieOptions)
    .json(new ApiResponse(200, {}, "Supplier logged out successfully"));
});

// Get Supplier Products
export const getSupplierProducts = asyncHandler(async (req, res) => {
  const { supplierId } = req.params;

  if (!supplierId) {
    throw new ApiError(400, 'Supplier ID is required');
  }

  const { Product } = await import('../models/Product.model.js');
  const products = await Product.find({ supplierId }).populate('supplierId');

  res.status(200).json(new ApiResponse(200, products, 'Supplier products fetched successfully'));
});

// Get Supplier Orders
export const getSupplierOrders = asyncHandler(async (req, res) => {
  const { supplierId } = req.params;

  if (!supplierId) {
    throw new ApiError(400, 'Supplier ID is required');
  }

  const { Order } = await import('../models/Order.model.js');
  const orders = await Order.find({ supplierId })
    .populate('productId')
    .populate('customerId')
    .populate('supplierId');

  res.status(200).json(new ApiResponse(200, orders, 'Supplier orders fetched successfully'));
});

// Get Supplier Dashboard Stats
export const getSupplierDashboardStats = asyncHandler(async (req, res) => {
  const { supplierId } = req.params;

  if (!supplierId) {
    throw new ApiError(400, 'Supplier ID is required');
  }

  const { Product } = await import('../models/Product.model.js');
  const { Order } = await import('../models/Order.model.js');

  // Get products count
  const totalProducts = await Product.countDocuments({ supplierId });

  // Get orders
  const orders = await Order.find({ supplierId });
  const activeOrders = orders.filter(order =>
    ['pending', 'confirmed', 'shipped'].includes(order.status)
  ).length;

  // Calculate earnings
  const totalEarnings = orders.reduce((sum, order) => sum + order.totalPrice, 0);

  const currentMonth = new Date().getMonth();
  const currentYear = new Date().getFullYear();
  const thisMonthEarnings = orders.filter(order => {
    const orderDate = new Date(order.orderDate);
    return orderDate.getMonth() === currentMonth && orderDate.getFullYear() === currentYear;
  }).reduce((sum, order) => sum + order.totalPrice, 0);

  const stats = {
    totalProducts,
    activeOrders,
    totalEarnings,
    thisMonthEarnings,
    totalOrders: orders.length,
    completedOrders: orders.filter(order => order.status === 'delivered').length
  };

  res.status(200).json(new ApiResponse(200, stats, 'Supplier dashboard stats fetched successfully'));
});
