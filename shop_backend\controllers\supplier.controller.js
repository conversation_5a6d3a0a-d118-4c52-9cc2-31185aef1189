import { Supplier } from '../models/Supplier.model.js';
import bcrypt from 'bcrypt';
import { asyncHandler } from '../utils/asyncHandler.js';
import { ApiError } from '../utils/ApiError.js';
import { ApiResponse } from '../utils/ApiResponse.js';

// Register Supplier
export const registerSupplier = asyncHandler(async (req, res) => {
  const { email, fullname, adhar, pan, contact, password, certificateCode } = req.body;

  if (!email || !fullname || !adhar || !pan || !contact || !password) {
    throw new ApiError(400, 'Required fields missing');
  }

  const existing = await Supplier.findOne({ email });
  if (existing) throw new ApiError(409, 'Supplier already exists');

  const hashedPassword = await bcrypt.hash(password, 10);

  const supplier = await Supplier.create({
    email, fullname, adhar, pan, contact, password: hashedPassword, certificateCode
  });

  res.status(201).json(new ApiResponse(201, supplier, 'Supplier registered successfully'));
});

// Get All Suppliers
export const getAllSuppliers = asyncHandler(async (req, res) => {
  const suppliers = await Supplier.find().populate('orders');
  res.status(200).json(new ApiResponse(200, suppliers, 'Suppliers fetched successfully'));
});

// Get Supplier by ID
export const getSupplierById = asyncHandler(async (req, res) => {
  const supplier = await Supplier.findById(req.params.id).populate('orders');

  if (!supplier) throw new ApiError(404, 'Supplier not found');

  res.status(200).json(new ApiResponse(200, supplier, 'Supplier fetched successfully'));
});

// Update Supplier
export const updateSupplier = asyncHandler(async (req, res) => {
  const supplier = await Supplier.findByIdAndUpdate(req.params.id, req.body, { new: true });

  if (!supplier) throw new ApiError(404, 'Supplier not found');

  res.status(200).json(new ApiResponse(200, supplier, 'Supplier updated successfully'));
});

// Delete Supplier
export const deleteSupplier = asyncHandler(async (req, res) => {
  const supplier = await Supplier.findByIdAndDelete(req.params.id);

  if (!supplier) throw new ApiError(404, 'Supplier not found');

  res.status(200).json(new ApiResponse(200, null, 'Supplier deleted successfully'));
});
// Verify Supplier
export const verifySupplier = asyncHandler(async (req, res) => {
  const { supplierId, certificateCode } = req.body;

  if (!supplierId || !certificateCode) {
    throw new ApiError(400, 'Required fields missing');
  }

  const supplier = await Supplier.findById(supplierId);
  if (!supplier) throw new ApiError(404, 'Supplier not found');

  if (supplier.certificateCode !== certificateCode) {
    throw new ApiError(400, 'Invalid certificate code');
  }

  supplier.isVerified = true;
  await supplier.save();

  res.status(200).json(new ApiResponse(200, supplier, 'Supplier verified successfully'));
});
