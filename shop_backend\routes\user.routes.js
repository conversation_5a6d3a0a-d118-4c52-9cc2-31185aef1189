import { Router } from "express";
import {
    registerUser,
    loginUser,
    getCurrentUser,
    updateUser,
    deleteUser,
    logoutUser
} from "../controllers/user.controller.js";
import { verifyJWT } from "../middlewares/auth.middleware.js";



const router = Router();
// User Registration Route  

router.post("/register", registerUser);

// User Login Route
router.post("/login", loginUser);

// Get Current Authenticated User
router.get("/current", verifyJWT, getCurrentUser);

// Update User
router.put("/:userId", verifyJWT, updateUser);
// Delete User
router.delete("/:userId", verifyJWT, deleteUser);

// Logout User
router.post("/logout", verifyJWT, logoutUser);

export default router;
