import mongoose from 'mongoose';
import dotenv from 'dotenv';
import bcrypt from 'bcrypt';
import { Supplier } from '../models/Supplier.model.js';
import connectDB from '../db/connection.db.js';

dotenv.config({ path: './.env' });

const createTestSupplier = async () => {
    try {
        await connectDB();
        console.log("Connected to MongoDB.");

        // Check if test supplier already exists
        const existingSupplier = await Supplier.findOne({ email: '<EMAIL>' });
        if (existingSupplier) {
            console.log("Test supplier already exists:");
            console.log({
                id: existingSupplier._id,
                email: existingSupplier.email,
                fullname: existingSupplier.fullname,
                isVerified: existingSupplier.isVerified
            });
            return;
        }

        // Create test supplier
        const hashedPassword = await bcrypt.hash('password123', 10);
        
        const testSupplier = await Supplier.create({
            email: '<EMAIL>',
            fullname: 'Test Supplier',
            adhar: '123456789012',
            pan: '**********',
            contact: '+1234567890',
            password: hashedPassword,
            certificateCode: 'TEST123',
            isVerified: true
        });

        console.log("Test supplier created successfully:");
        console.log({
            id: testSupplier._id,
            email: testSupplier.email,
            fullname: testSupplier.fullname,
            isVerified: testSupplier.isVerified
        });

    } catch (error) {
        console.error("Error creating test supplier:", error);
    } finally {
        mongoose.disconnect();
        console.log("Disconnected from MongoDB.");
    }
};

createTestSupplier();
