import { Router } from "express";
import {
  createOrder,
  getAllOrders,
  getOrderById,
  updateOrder,
  deleteOrder
} from "../controllers/order.controller.js";
import { verifyJWT } from "../middlewares/auth.middleware.js";

const router = Router();

// Create new order
router.post("/", verifyJWT, createOrder);

// Get all orders
router.get("/", verifyJWT, getAllOrders);

// Get single order
router.get("/:id", verifyJWT, getOrderById);

// Update order
router.put("/:id", verifyJWT, updateOrder);

// Delete order
router.delete("/:id", verifyJWT, deleteOrder);

export default router;
