import { Router } from "express";
import {
  registerSupplier,
  getAllSuppliers,
  getSupplierById,
  updateSupplier,
  deleteSupplier
} from "../controllers/supplier.controller.js";
import { verifyJWT } from "../middlewares/auth.middleware.js";
import { verifySupplier } from "../middlewares/supplierAuth.middleware.js";

const router = Router();

// Register new supplier
router.post("/register", registerSupplier);

// Get all suppliers
router.get("/", getAllSuppliers);

// Get a specific supplier
router.get("/:id", getSupplierById);

// Update a supplier
router.put("/:id", verifyJWT, verifySupplier, updateSupplier);

// Delete a supplier
router.delete("/:id", verifyJWT, verifySupplier, deleteSupplier);

export default router;
