import { Router } from "express";
import {
  registerSupplier,
  loginSupplier,
  logoutSupplier,
  getAllSuppliers,
  getSupplierById,
  updateSupplier,
  deleteSupplier,
  verifySupplier,
  getSupplierProducts,
  getSupplierOrders,
  getSupplierDashboardStats
} from "../controllers/supplier.controller.js";
import { verifyJWT } from "../middlewares/auth.middleware.js";
import { verifySupplier as verifySupplierMiddleware } from "../middlewares/supplierAuth.middleware.js";

const router = Router();

// Register new supplier
router.post("/register", registerSupplier);

// Login supplier
router.post("/login", loginSupplier);

// Logout supplier
router.post("/logout", verifyJWT, logoutSupplier);

// Verify supplier
router.post("/verify", verifySupplier);

// Get all suppliers
router.get("/", getAllSuppliers);

// Get a specific supplier
router.get("/:id", getSupplierById);

// Get supplier products
router.get("/:supplierId/products", verifyJWT, getSupplierProducts);

// Get supplier orders
router.get("/:supplierId/orders", verifyJWT, getSupplierOrders);

// Get supplier dashboard stats
router.get("/:supplierId/dashboard-stats", verifyJWT, getSupplierDashboardStats);

// Update a supplier
router.put("/:id", verifyJWT, verifySupplierMiddleware, updateSupplier);

// Delete a supplier
router.delete("/:id", verifyJWT, verifySupplierMiddleware, deleteSupplier);

export default router;
