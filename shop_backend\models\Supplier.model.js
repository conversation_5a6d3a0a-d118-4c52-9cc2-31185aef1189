// models/Supplier.js
import mongoose from 'mongoose';

const supplierSchema = new mongoose.Schema({
  email: { type: String, required: true, unique: true },
  fullname: { type: String, required: true },
  adhar: { type: String, required: true },
  pan: { type: String, required: true },
  contact: { type: String, required: true },
  password: { type: String, required: true },

  certificateCode: { type: String }, // or `verifiedCode`
  isVerified: { type: Boolean, default: false },

  orders: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Order' }],
}, { timestamps: true });

export const Supplier = mongoose.model('Supplier', supplierSchema);
