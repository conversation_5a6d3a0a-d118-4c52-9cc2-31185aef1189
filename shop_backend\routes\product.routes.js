import { Router } from "express";
import {
  createProduct,
  getAllProducts,
  getProductById,
  updateProduct,
  deleteProduct,
  createProductBySupplier,
  getProductsBySupplier
} from "../controllers/product.controller.js";
import { verifyJWT } from "../middlewares/auth.middleware.js";

const router = Router();

// Create new product
router.post("/", verifyJWT, createProduct);

// Create product by supplier (authenticated supplier)
router.post("/supplier", verifyJWT, createProductBySupplier);

// Get all products
router.get("/", getAllProducts); // Publicly accessible

// Get products by supplier
router.get("/supplier/:supplierId", getProductsBySupplier);

// Get a single product by ID
router.get("/:id", getProductById); // Publicly accessible

// Update product
router.put("/:id", verifyJWT, updateProduct);

// Delete product
router.delete("/:id", verifyJWT, deleteProduct);

export default router;
