import { Router } from "express";
import {
  createProduct,
  getAllProducts,
  getProductById,
  updateProduct,
  deleteProduct
} from "../controllers/product.controller.js";
import { verifyJWT } from "../middlewares/auth.middleware.js";

const router = Router();

// Create new product
router.post("/", verifyJWT, createProduct);

// Get all products
router.get("/", getAllProducts); // Publicly accessible

// Get a single product by ID
router.get("/:id", getProductById); // Publicly accessible

// Update product
router.put("/:id", verifyJWT, updateProduct);

// Delete product
router.delete("/:id", verifyJWT, deleteProduct);

export default router;
