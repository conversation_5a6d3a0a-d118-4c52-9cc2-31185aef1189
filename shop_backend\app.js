import express from "express"
import cors from "cors"
import cookieparser from "cookie-parser"

const app = express()

app.use(cors({
    origin:process.env.CORS_ORIGIN,
    credentials:true
}))

app.use(express.static("public"))
app.use(cookieparser())


app.use(express.json({limit: "1000000kb"}))
app.use(express.urlencoded({extended: true, limit: "100000000kb"}))     

import userRoutes from "./routes/user.routes.js";
import productRoutes from "./routes/product.routes.js";
import orderRoutes from "./routes/order.routes.js";
import supplierRoutes from "./routes/supllier.routes.js";

app.use("/user", userRoutes);
app.use("/product", productRoutes);
app.use("/order", orderRoutes);
app.use("/supplier", supplierRoutes);

export {app}