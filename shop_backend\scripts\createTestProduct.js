import mongoose from 'mongoose';
import dotenv from 'dotenv';
import { Supplier } from '../models/Supplier.model.js';
import { Product } from '../models/Product.model.js';
import connectDB from '../db/connection.db.js';

dotenv.config({ path: './.env' });

const createTestProduct = async () => {
    try {
        await connectDB();
        console.log("Connected to MongoDB.");

        // Find the test supplier
        const testSupplier = await Supplier.findOne({ email: '<EMAIL>' });
        if (!testSupplier) {
            console.log("Test supplier not found. Please run createTestSupplier.js first.");
            return;
        }

        console.log("Found test supplier:", testSupplier.fullname);

        // Check if test products already exist
        const existingProducts = await Product.find({ supplierId: testSupplier._id });
        if (existingProducts.length > 0) {
            console.log(`Test supplier already has ${existingProducts.length} products:`);
            existingProducts.forEach(product => {
                console.log(`- ${product.type}: ₹${product.pricePerUnit}/unit`);
            });
            return;
        }

        // Create test products
        const testProducts = [
            {
                supplierId: testSupplier._id,
                type: 'Fresh Apples',
                pricePerUnit: 120,
                dateOfProduce: new Date('2024-01-15'),
                review: 'Premium quality red apples, fresh from the orchard'
            },
            {
                supplierId: testSupplier._id,
                type: 'Organic Bananas',
                pricePerUnit: 80,
                dateOfProduce: new Date('2024-01-16'),
                review: 'Organic bananas, perfect for smoothies and snacks'
            },
            {
                supplierId: testSupplier._id,
                type: 'Farm Fresh Milk',
                pricePerUnit: 60,
                dateOfProduce: new Date('2024-01-17'),
                review: 'Pure cow milk from local dairy farm'
            }
        ];

        const createdProducts = await Product.insertMany(testProducts);
        
        console.log(`Created ${createdProducts.length} test products:`);
        createdProducts.forEach(product => {
            console.log(`- ${product.type}: ₹${product.pricePerUnit}/unit`);
        });

    } catch (error) {
        console.error("Error creating test products:", error);
    } finally {
        mongoose.disconnect();
        console.log("Disconnected from MongoDB.");
    }
};

createTestProduct();
